import 'server-only';
import { getServerSession } from 'next-auth';
import { redirect } from 'next/navigation';
import { APP_CONFIG } from './config';

export async function getRequiredSession() {
  const session = await getServerSession();
  
  if (!session?.user) {
    redirect(APP_CONFIG.routes.login);
  }
  
  return session;
}

export async function getCurrentUser() {
  const session = await getRequiredSession();
  return session.user;
}